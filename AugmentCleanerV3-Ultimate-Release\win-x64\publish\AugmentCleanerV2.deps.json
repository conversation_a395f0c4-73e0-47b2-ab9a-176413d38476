{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0/win-x64", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {}, ".NETCoreApp,Version=v9.0/win-x64": {"AugmentCleanerV2/1.0.0": {"dependencies": {"System.Data.SQLite": "1.0.119", "runtimepack.Microsoft.NETCore.App.Runtime.win-x64": "9.0.8", "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64": "9.0.8"}, "runtime": {"AugmentCleanerV2.dll": {}}}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.8": {"runtime": {"Microsoft.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "Microsoft.VisualBasic.Core.dll": {"assemblyVersion": "1*******", "fileVersion": "14.0.825.36511"}, "Microsoft.Win32.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "Microsoft.Win32.Registry.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.AppContext.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Buffers.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Collections.Concurrent.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Collections.NonGeneric.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Collections.Specialized.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Collections.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.ComponentModel.DataAnnotations.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.ComponentModel.EventBasedAsync.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.ComponentModel.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.ComponentModel.TypeConverter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.ComponentModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Data.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Data.DataSetExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Data.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.Contracts.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.FileVersionInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.Process.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.StackTrace.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.TextWriterTraceListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.Tools.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.TraceSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.Tracing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Drawing.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Dynamic.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Formats.Tar.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Globalization.Calendars.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Globalization.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Globalization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.Compression.Brotli.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.Compression.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.Compression.ZipFile.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.Compression.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.FileSystem.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.FileSystem.DriveInfo.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.FileSystem.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.FileSystem.Watcher.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.FileSystem.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.IsolatedStorage.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.MemoryMappedFiles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.Pipes.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.Pipes.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.UnmanagedMemoryStream.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Linq.Expressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Linq.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Linq.Queryable.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.Http.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.Mail.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.NameResolution.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.NetworkInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.Ping.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.Quic.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.Requests.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.ServicePoint.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.Sockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.WebClient.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.WebHeaderCollection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.WebProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.WebSockets.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.WebSockets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Net.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Numerics.Vectors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.ObjectModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Private.CoreLib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Private.DataContractSerialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Private.Uri.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Private.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Private.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Reflection.DispatchProxy.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Reflection.Emit.ILGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Reflection.Emit.Lightweight.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Reflection.Emit.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Reflection.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Reflection.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Reflection.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Reflection.TypeExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Reflection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Resources.Reader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Resources.ResourceManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Resources.Writer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.CompilerServices.VisualC.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.Handles.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.InteropServices.JavaScript.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.InteropServices.RuntimeInformation.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.InteropServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.Intrinsics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.Loader.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.Numerics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.Serialization.Formatters.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.Serialization.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.Serialization.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.Serialization.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Claims.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.Algorithms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.Cng.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.Csp.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.OpenSsl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.X509Certificates.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Principal.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Principal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.SecureString.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.ServiceModel.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.ServiceProcess.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Text.Encoding.CodePages.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Text.Encoding.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Text.Encoding.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Text.RegularExpressions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.Channels.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.Overlapped.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.Tasks.Dataflow.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.Tasks.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.Tasks.Parallel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.Tasks.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.Thread.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.ThreadPool.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.Timer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Transactions.Local.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Transactions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.ValueTuple.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Web.HttpUtility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Windows.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Xml.Linq.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Xml.ReaderWriter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Xml.Serialization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Xml.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Xml.XPath.XDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Xml.XPath.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Xml.XmlDocument.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Xml.XmlSerializer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "WindowsBase.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "mscorlib.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "netstandard.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}, "native": {"Microsoft.DiaSymReader.Native.amd64.dll": {"fileVersion": "14.42.34436.0"}, "System.IO.Compression.Native.dll": {"fileVersion": "9.0.825.36511"}, "clretwrc.dll": {"fileVersion": "9.0.825.36511"}, "clrgc.dll": {"fileVersion": "9.0.825.36511"}, "clrgcexp.dll": {"fileVersion": "9.0.825.36511"}, "clrjit.dll": {"fileVersion": "9.0.825.36511"}, "coreclr.dll": {"fileVersion": "9.0.825.36511"}, "createdump.exe": {"fileVersion": "9.0.825.36511"}, "hostfxr.dll": {"fileVersion": "9.0.825.36511"}, "hostpolicy.dll": {"fileVersion": "9.0.825.36511"}, "mscordaccore.dll": {"fileVersion": "9.0.825.36511"}, "mscordaccore_amd64_amd64_9.0.825.36511.dll": {"fileVersion": "9.0.825.36511"}, "mscordbi.dll": {"fileVersion": "9.0.825.36511"}, "mscorrc.dll": {"fileVersion": "9.0.825.36511"}, "msquic.dll": {"fileVersion": "*******"}}}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/9.0.8": {"runtime": {"Accessibility.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}, "Microsoft.VisualBasic.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}, "Microsoft.VisualBasic.dll": {"assemblyVersion": "********", "fileVersion": "9.0.825.36801"}, "Microsoft.Win32.Registry.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "Microsoft.Win32.SystemEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}, "System.Diagnostics.EventLog.Messages.dll": {"assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.DirectoryServices.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Drawing.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}, "System.Drawing.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}, "System.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}, "System.Formats.Nrbf.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Private.Windows.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}, "System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Security.Permissions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Threading.AccessControl.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Windows.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}, "System.Windows.Forms.Design.Editors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}, "System.Windows.Forms.Design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}, "System.Windows.Forms.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}, "System.Windows.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36801"}}}, "EntityFramework/6.4.4": {"dependencies": {"Microsoft.CSharp": "4.7.0", "System.CodeDom": "4.7.0", "System.ComponentModel.Annotations": "4.7.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Data.SqlClient": "4.8.1"}, "runtime": {"lib/netstandard2.1/EntityFramework.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "6.400.420.21404"}, "lib/netstandard2.1/EntityFramework.dll": {"assemblyVersion": "*******", "fileVersion": "6.400.420.21404"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "4.7.0"}}, "Microsoft.Win32.SystemEvents/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0"}}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"native": {"runtimes/win-x64/native/sni.dll": {"fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "native": {"runtimes/win-x64/native/SQLite.Interop.dll": {"fileVersion": "*********"}}}, "System.CodeDom/4.7.0": {}, "System.ComponentModel.Annotations/4.7.0": {}, "System.Configuration.ConfigurationManager/4.7.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.7.0", "System.Security.Permissions": "4.7.0"}}, "System.Data.SqlClient/4.8.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "System.Security.Principal.Windows": "4.7.0", "runtime.native.System.Data.SqlClient.sni": "4.7.0"}, "runtime": {"runtimes/win/lib/netcoreapp2.1/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.6702"}}}, "System.Data.SQLite/1.0.119": {"dependencies": {"System.Data.SQLite.Core": "1.0.119", "System.Data.SQLite.EF6": "1.0.119"}}, "System.Data.SQLite.Core/1.0.119": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "1.0.119"}}, "System.Data.SQLite.EF6/1.0.119": {"dependencies": {"EntityFramework": "6.4.4"}, "runtime": {"lib/netstandard2.1/System.Data.SQLite.EF6.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "System.Drawing.Common/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "Microsoft.Win32.SystemEvents": "4.7.0"}}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "4.7.0"}}, "System.Security.Cryptography.ProtectedData/4.7.0": {}, "System.Security.Permissions/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Windows.Extensions": "4.7.0"}}, "System.Security.Principal.Windows/4.7.0": {}, "System.Windows.Extensions/4.7.0": {"dependencies": {"System.Drawing.Common": "4.7.0"}}}}, "libraries": {"AugmentCleanerV2/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.NETCore.App.Runtime.win-x64/9.0.8": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "runtimepack.Microsoft.WindowsDesktop.App.Runtime.win-x64/9.0.8": {"type": "runtimepack", "serviceable": false, "sha512": ""}, "EntityFramework/6.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-yj1+/4tci7Panu3jKDHYizxwVm0Jvm7b7m057b5h4u8NUHGCR8WIWirBTw+8EptRffwftIWPBeIRGNKD1ewEMQ==", "path": "entityframework/6.4.4", "hashPath": "entityframework.6.4.4.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "Microsoft.Win32.SystemEvents/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-mtVirZr++rq+XCDITMUdnETD59XoeMxSpLRIII7JRI6Yj0LEDiO1pPn0ktlnIj12Ix8bfvQqQDMMIF9wC98oCA==", "path": "microsoft.win32.systemevents/4.7.0", "hashPath": "microsoft.win32.systemevents.4.7.0.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-9kyFSIdN3T0qjDQ2R0HRXYIhS3l5psBzQi6qqhdLz+SzFyEy4sVxNOke+yyYv8Cu8rPER12c3RDjLT8wF3WBYQ==", "path": "runtime.native.system.data.sqlclient.sni/4.7.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.7.0.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-dI7ngiCNgdm+n00nQvFTa+LbHvE9MIQXwMSLRzJI/KAJ7G1WmCachsvfE1CD6xvb3OXJvYYEfv3+S/LHyhN0Rg==", "path": "stub.system.data.sqlite.core.netstandard/1.0.119", "hashPath": "stub.system.data.sqlite.core.netstandard.1.0.119.nupkg.sha512"}, "System.CodeDom/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Hs9pw/kmvH3lXaZ1LFKj3pLQsiGfj2xo3sxSzwiLlRL6UcMZUTeCfoJ9Udalvn3yq5dLlPEZzYegrTQ1/LhPOQ==", "path": "system.codedom/4.7.0", "hashPath": "system.codedom.4.7.0.nupkg.sha512"}, "System.ComponentModel.Annotations/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-0YFqjhp/mYkDGpU0Ye1GjE53HMp9UVfGN7seGpAMttAC0C40v5gw598jCgpbBLMmCo0E5YRLBv5Z2doypO49ZQ==", "path": "system.componentmodel.annotations/4.7.0", "hashPath": "system.componentmodel.annotations.4.7.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/anOTeSZCNNI2zDilogWrZ8pNqCmYbzGNexUnNhjW8k0sHqEZ2nHJBp147jBV3hGYswu5lINpNg1vxR7bnqvVA==", "path": "system.configuration.configurationmanager/4.7.0", "hashPath": "system.configuration.configurationmanager.4.7.0.nupkg.sha512"}, "System.Data.SqlClient/4.8.1": {"type": "package", "serviceable": true, "sha512": "sha512-HKLykcv6eZLbLnSMnlQ6Os4+UAmFE+AgYm92CTvJYeTOBtOYusX3qu8OoGhFrnKZax91UcLcDo5vPrqvJUTSNQ==", "path": "system.data.sqlclient/4.8.1", "hashPath": "system.data.sqlclient.4.8.1.nupkg.sha512"}, "System.Data.SQLite/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-JSOJpnBf9goMnxGTJFGCmm6AffxgtpuXNXV5YvWO8UNC2zwd12qkUe5lAbnY+2ohIkIukgIjbvR1RA/sWILv3w==", "path": "system.data.sqlite/1.0.119", "hashPath": "system.data.sqlite.1.0.119.nupkg.sha512"}, "System.Data.SQLite.Core/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-bhQB8HVtRA+OOYw8UTD1F1kU+nGJ0/OZvH1JmlVUI4bGvgVEWeX1NcHjA765NvUoRVuCPlt8PrEpZ1thSsk1jg==", "path": "system.data.sqlite.core/1.0.119", "hashPath": "system.data.sqlite.core.1.0.119.nupkg.sha512"}, "System.Data.SQLite.EF6/1.0.119": {"type": "package", "serviceable": true, "sha512": "sha512-BwwgCSeA80gsxdXtU7IQEBrN9kQXWQrD11hNYOJZbXBBI1C4r7hA4QhBAalO1nzijXikthGRUADIEMI3nlucLA==", "path": "system.data.sqlite.ef6/1.0.119", "hashPath": "system.data.sqlite.ef6.1.0.119.nupkg.sha512"}, "System.Drawing.Common/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-v+XbyYHaZjDfn0ENmJEV1VYLgGgCTx1gnfOBcppowbpOAriglYgGCvFCPr2EEZyBvXlpxbEsTwkOlInl107ahA==", "path": "system.drawing.common/4.7.0", "hashPath": "system.drawing.common.4.7.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "System.Security.Permissions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-dkOV6YYVBnYRa15/yv004eCGRBVADXw8qRbbNiCn/XpdJSUXkkUeIvdvFHkvnko4CdKMqG8yRHC4ox83LSlMsQ==", "path": "system.security.permissions/4.7.0", "hashPath": "system.security.permissions.4.7.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ojD0PX0XhneCsUbAZVKdb7h/70vyYMDYs85lwEI+LngEONe/17A0cFaRFqZU+sOEidcVswYWikYOQ9PPfjlbtQ==", "path": "system.security.principal.windows/4.7.0", "hashPath": "system.security.principal.windows.4.7.0.nupkg.sha512"}, "System.Windows.Extensions/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-CeWTdRNfRaSh0pm2gDTJFwVaXfTq6Xwv/sA887iwPTneW7oMtMlpvDIO+U60+3GWTB7Aom6oQwv5VZVUhQRdPQ==", "path": "system.windows.extensions/4.7.0", "hashPath": "system.windows.extensions.4.7.0.nupkg.sha512"}}, "runtimes": {"win-x64": ["win", "any", "base"]}}